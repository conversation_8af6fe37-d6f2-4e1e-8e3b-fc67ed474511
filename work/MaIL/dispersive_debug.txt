
2025-09-02 20:45:17 - CONFIGURE DISPERSIVE LOSS
======================================
- use_disperse_loss: True
- disperse_weight: 0.5
- disperse_in_diffusion: False
- use_dispersive_in_diffusion: False
- model type: Diffusion
- has disp_loss method: True


2025-09-02 20:48:57 - CONFIGURE DISPERSIVE LOSS
======================================
- use_disperse_loss: True
- disperse_weight: 0.5
- disperse_in_diffusion: False
- use_dispersive_in_diffusion: False
- model type: Diffusion
- has disp_loss method: True


2025-09-02 20:50:43 - CONFIGURE DISPERSIVE LOSS
======================================
- use_disperse_loss: True
- disperse_weight: 0.5
- disperse_in_diffusion: True
- use_dispersive_in_diffusion: True
- model type: Diffusion
- has disp_loss method: True


2025-09-02 22:13:25 - CONFIGURE DISPERSIVE LOSS
======================================
- use_disperse_loss: True
- disperse_weight: 0.5
- disperse_in_diffusion: True
- use_dispersive_in_diffusion: True
- model type: Diffusion
- has disp_loss method: True


2025-09-02 22:14:56 - CONFIGURE DISPERSIVE LOSS
======================================
- use_disperse_loss: True
- disperse_weight: 0.5
- disperse_in_diffusion: True
- use_dispersive_in_diffusion: True
- model type: Diffusion
- has disp_loss method: True


2025-09-02 22:31:54 - CONFIGURE DISPERSIVE LOSS
======================================
- use_disperse_loss: True
- disperse_weight: 0.5
- disperse_in_diffusion: True
- use_dispersive_in_diffusion: True
- model type: Diffusion
- has disp_loss method: True

