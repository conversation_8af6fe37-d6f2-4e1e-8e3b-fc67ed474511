# DC Tokens 完整指南 - MaIL 项目

## 目录

1. [概述](#概述)
2. [实现细节](#实现细节)
3. [训练指南](#训练指南)
4. [使用方法](#使用方法)
5. [rollout](#rollout)
6. [故障排除](#故障排除)
7. [扩展和自定义](#扩展和自定义)

## 概述

本文档提供了在 MaIL 项目中集成 DC (Discriminative/Contrastive) tokens 的完整指南，包括实现细节和训练方法。该实现参考了 SoftREPA 项目的设计理念，旨在通过轻量级的对比学习增强模型性能。

### 核心设计理念

与 SoftREPA 项目类似，我们的实现遵循以下原则：
1. **轻量级微调**：只添加少量可学习参数（<1M）
2. **层级注入**：在 encoder 的前 n 层注入 DC tokens
3. **时间依赖性**：支持时间相关的 DC tokens
4. **序列完整性**：保持原始序列长度不变

## 实现细节

### 1. 参数定义

在 `DiffusionEncDec` 类的 `__init__` 方法中添加了以下参数：

```python
# DC tokens 配置
self.n_dc_tokens = n_dc_tokens      # 每层的DC token数量
self.n_dc_layers = n_dc_layers      # 使用DC tokens的层数
self.use_dc_t = use_dc_t           # 是否使用时间相关DC tokens
self.use_dc = use_dc               # 是否启用DC tokens

# DC tokens 参数初始化
if self.use_dc and self.n_dc_tokens > 0 and self.n_dc_layers > 0:
    # 形状: (n_dc_layers, n_dc_tokens, embed_dim)
    self.dc_tokens = nn.Parameter(torch.randn(self.n_dc_layers, self.n_dc_tokens, embed_dim))
    nn.init.normal_(self.dc_tokens, mean=0, std=0.02)
    
    if self.use_dc_t:
        # 时间步嵌入: 100个离散时间步
        self.dc_t_tokens = nn.Embedding(100, embed_dim * self.n_dc_layers)
        nn.init.normal_(self.dc_t_tokens.weight, mean=0, std=0.02)
```

### 2. 时间嵌入处理

在 forward 方法中处理时间相关的 DC tokens：

```python
# 准备DC tokens相关的时间嵌入
dc_time_emb = None
if self.use_dc and self.use_dc_t:
    # 时间步离散化处理，类似SoftREPA的实现
    int_t = time.type(torch.int32)
    if torch.sum(int_t >= 100) > 0:  # 处理边界情况
        int_t = torch.clamp(int_t, 0, 99)
    dc_time_emb = self.dc_t_tokens(int_t).contiguous()
    dc_time_emb = dc_time_emb.chunk(self.n_dc_layers, dim=-1)  # 按层分块
```

### 3. TransformerEncoder 修改

修改了 `TransformerEncoder` 的 forward 方法，支持在每一层前注入 DC tokens：

```python
def forward(self, x, dc_tokens=None, dc_time_emb=None):
    for layer_idx, layer in enumerate(self.blocks):
        # 在前n_dc_layers层注入DC tokens
        if (dc_tokens is not None and layer_idx < self.n_dc_layers and 
            layer_idx < dc_tokens.shape[0]):
            
            batch_size = x.shape[0]
            n_dc_tokens = dc_tokens.shape[1]
            
            # 获取当前层的DC tokens
            current_dc = dc_tokens[layer_idx].unsqueeze(0).expand(batch_size, -1, -1)
            
            # 添加时间信息（如果启用）
            if dc_time_emb is not None and layer_idx < len(dc_time_emb):
                dc_t = dc_time_emb[layer_idx].unsqueeze(1).expand(-1, n_dc_tokens, -1)
                current_dc = current_dc + dc_t
            
            # 将DC tokens拼接到输入序列前面
            x = torch.cat([current_dc, x], dim=1)
        
        # 通过当前层
        x = layer(x)
        
        # 移除DC tokens，保持原始序列长度
        if (dc_tokens is not None and layer_idx < self.n_dc_layers and 
            layer_idx < dc_tokens.shape[0]):
            x = x[:, n_dc_tokens:, :]
    
    return self.ln(x)
```

### 4. 主要调用流程

在 `DiffusionEncDec.forward` 中的调用：

```python
# 将DC tokens传递给encoder，让它在每一层前进行拼接
if self.use_dc and self.n_dc_tokens > 0:
    encoder_output = self.encoder(input_seq, dc_tokens=self.dc_tokens, dc_time_emb=dc_time_emb)
else:
    encoder_output = self.encoder(input_seq)
```

### 5. 测试结果

#### 基础功能测试
- ✅ 模型创建成功
- ✅ DC tokens 参数正确初始化
- ✅ 前向传播正常工作
- ✅ 梯度计算正确
- ✅ 参数统计：DC相关参数占比约0.72%

#### 注入机制测试
- ✅ 前 n_dc_layers 层成功注入 DC tokens
- ✅ 序列长度在注入后正确增加，处理后正确恢复
- ✅ 时间相关 DC tokens 正确添加
- ✅ 非DC层正常处理原始序列

## 训练指南

### 文件结构

```
MaIL/
├── train_dc_tokens.py          # 主训练脚本
├── run_dc_training.sh          # 启动脚本
├── config/
│   ├── dc_training.yaml        # DC训练主配置
│   └── agents/
│       └── dc_ddpm.yaml        # DC agent配置
├── agents/
│   └── dc_ddpm_agent.py        # DC agent实现
└── logs/dc_training/           # 训练日志目录
```

### 快速开始

#### 1. 基本训练

```bash
# 使用默认配置进行训练
./run_dc_training.sh

# 或者直接使用Python
python train_dc_tokens.py
```

#### 2. 调试模式

```bash
# 快速调试运行（少量步数和小批次）
./run_dc_training.sh --debug
```

#### 3. 自定义参数

```bash
# 自定义训练步数和批次大小
./run_dc_training.sh --steps 5000 --batch-size 16

# 自定义学习率和温度参数
./run_dc_training.sh --lr 2e-4 --temp 0.05

# 设置DC tokens数量
./run_dc_training.sh --dc-tokens 8
```

#### 4. 从预训练模型开始

```bash
# 从预训练模型开始DC训练
conda activate mail&& cd /home/<USER>/work/MaIL && export http_proxy=http://localhost:7890 && export https_proxy=http://localhost:7890 && CUDA_VISIBLE_DEVICES=3 ./run_dc_training.sh --pretrained /home/<USER>/work/MaIL/MaIL/logs/libero_goal/multi_task_diffusion_transformer/sweeps/goal_ddpm_transformer_encdec/2025-07-22/21-53-31/eval_best_ddpm.pth --dweight 0.1
```

#### 5. 禁用Wandb

```bash
# 不使用wandb记录
./run_dc_training.sh --no-wandb
```

### 配置文件说明

#### dc_training.yaml

主要配置文件，包含：

- **基本设置**: 实验名称、任务套件、设备配置
- **DC训练参数**: 训练步数、批次大小、学习率等
- **对比学习参数**: 温度、缩放因子、对角权重
- **数据集配置**: 数据路径、观察模态、序列长度
- **日志和检查点**: Wandb配置、保存频率等

#### agents/dc_ddpm.yaml

Agent配置文件，包含：

- **模型架构**: Diffusion模型、编码器、解码器配置
- **DC参数**: DC层数、DC tokens数量、DC相关设置
- **优化器**: 学习率、权重衰减等
- **EMA设置**: 指数移动平均配置

### 训练流程

1. **初始化**: 加载配置、设置随机种子、初始化日志
2. **Agent创建**: 实例化DiffusionAgent_DC
3. **DC设置**: 初始化DC tokens、冻结基础模型（可选）
4. **训练循环**:
   - 数据加载和格式转换
   - 计算对比误差矩阵
   - 计算对比损失
   - 反向传播和参数更新
   - 定期评估和保存检查点

### 关键特性

#### 1. SoftREPA风格对比学习
- 创建扩展批次：每个动作与所有观察配对
- 计算对比误差矩阵
- 使用温度缩放的对比损失

#### 2. DC Tokens集成
- 支持编码器和解码器DC tokens
- 可配置的DC层数和tokens数量
- 时间依赖的DC tokens（dc_t）

#### 3. 灵活的训练模式
- 可选择冻结基础模型，只训练DC参数
- 支持从预训练模型开始
- EMA支持用于稳定训练

#### 4. 完整的监控和日志
- Wandb集成用于实验跟踪
- 详细的训练和验证日志
- 自动检查点保存和清理

## 使用方法

### 创建带DC tokens的模型

```python
model = DiffusionEncDec(
    encoder=encoder_config,
    decoder=decoder_config,
    state_dim=state_dim,
    action_dim=action_dim,
    device=device,
    goal_conditioned=True,
    embed_dim=embed_dim,
    embed_pdrob=0.1,
    goal_seq_len=goal_seq_len,
    obs_seq_len=obs_seq_len,
    action_seq_len=action_seq_len,
    goal_drop=0.1,
    linear_output=False,
    n_dc_layers=3,        # 前3层使用DC tokens
    n_dc_tokens=4,        # 每层4个DC tokens
    use_dc_t=True,        # 启用时间相关DC tokens
    use_dc=True           # 启用DC tokens
)
```

### 训练时只优化DC tokens

```python
# 设置只有DC tokens可训练
for name, param in model.named_parameters():
    if 'dc' in name:
        param.requires_grad = True
    else:
        param.requires_grad = False

# 创建优化器
dc_params = [p for name, p in model.named_parameters() if 'dc' in name and p.requires_grad]
optimizer = torch.optim.AdamW(dc_params, lr=1e-4)
```

### 输出文件

#### 日志文件
- `logs/dc_training/YYYYMMDD_HHMMSS/dc_main.log`: 主要训练日志
- `logs/dc_training/YYYYMMDD_HHMMSS/dc_training.log`: 详细训练日志

#### 检查点文件
- `checkpoints/dc_training/YYYYMMDD_HHMMSS/best_dc_model.pth`: 最佳模型
- `checkpoints/dc_training/YYYYMMDD_HHMMSS/latest_dc_model.pth`: 最新模型
- `checkpoints/dc_training/YYYYMMDD_HHMMSS/checkpoint_step_*.pth`: 定期检查点


## rollout

### 方式1: 训练完成后自动评估

DC训练完成后，`train_dc_tokens.py`会自动运行单进程评估，分别评估最佳模型和最新模型。

**自动评估流程：**

1. **训练完成后自动开始评估**
```
🚀 开始DC模型自动评估阶段...
============================================================

📊 开始评估 最佳模型: best_dc_model.pth
🔄 导入评估模块...
📥 加载完整DC模型: /path/to/checkpoints/best_dc_model.pth
🎮 开始环境评估...
🎯 最佳模型评估完成:
  - 整体成功率: 0.875 (175/200)
  - 平均任务成功率: 0.863
  - 结果文件: dc_evaluation_best_dc_model_0805_1430.json

📊 开始评估 最新模型: latest_dc_model.pth
🔄 导入评估模块...
📥 加载完整DC模型: /path/to/checkpoints/latest_dc_model.pth
🎮 开始环境评估...
🎯 最新模型评估完成:
  - 整体成功率: 0.850 (170/200)
  - 平均任务成功率: 0.841
  - 结果文件: dc_evaluation_latest_dc_model_0805_1430.json
```

2. **评估结果总结**
```
📊 DC模型评估结果总结:
============================================================
  best_dc_model.pth:
    整体成功率: 0.875 (175/200)
    平均任务成功率: 0.863
    结果文件: dc_evaluation_best_dc_model_0805_1430.json
  latest_dc_model.pth:
    整体成功率: 0.850 (170/200)
    平均任务成功率: 0.841
    结果文件: dc_evaluation_latest_dc_model_0805_1430.json
============================================================
```

**自动评估特点：**
- 🔄 **单进程执行**：避免复杂的多进程配置
- 📊 **双模型评估**：同时评估best和latest模型
- 💾 **自动保存结果**：评估结果自动保存为JSON文件
- 📈 **Wandb记录**：评估指标自动记录到Wandb
- 🎬 **视频录制**：自动录制成功和失败的视频片段

### 方式2: 直接命令行

#### 1.评估基线模型
```bash
cd /home/<USER>/work/MaIL
CUDA_VISIBLE_DEVICES=4 python load_and_simulate.py \
    --config config/benchmark_libero_goal_diffusion_transformer.yaml \
    --weights /home/<USER>/work/MaIL/logs/libero_10/multi_task_diffusion_transformer/sweeps/disperse/2025-08-06/22-16-04/last_ddpm.pth \
    --task_suite libero_10 \
    --num_episodes 20 \
    --max_steps 600 \
    --seed 4 \
    --save_video \
    --max_videos_per_task 2 \
    --output "8_29_simulation_results.json" \
    --verbose
```

#### 2.评估DC预训练模型
```bash
# 评估最佳DC模型
cd /home/<USER>/work/MaIL && conda activate mail && CUDA_VISIBLE_DEVICES=1 python load_and_simulate.py --complete_dc_model "/home/<USER>/work/MaIL/logs/dc_training/2025-08-07/11-46-01/checkpoints/dc_training/libero_goal_20250807_114631/best_dc_model.pth" --task_suite libero_goal --num_episodes 20 --max_steps 600 --max_videos_per_task 3 --verbose --output "dc_full_libero_goal_evaluation_8_7_4.json"

# 评估最新DC模型
CUDA_VISIBLE_DEVICES=2 python load_and_simulate.py --complete_dc_model "/home/<USER>/work/MaIL/logs/dc_training/2025-08-04/22-20-31/checkpoints/dc_training/libero_goal_20250804_222039/latest_dc_model.pth" --task_suite libero_goal --num_episodes 20 --max_steps 600 --max_videos_per_task 3 --verbose --output "dc_full_libero_goal_evaluation_8_5_5.json"
```

## 故障排除

### 常见问题

1. **CUDA内存不足**
   ```bash
   # 减少批次大小
   ./run_dc_training.sh --batch-size 4
   ```

2. **配置文件错误**
   ```bash
   # 检查配置文件语法
   python -c "from omegaconf import OmegaConf; OmegaConf.load('config/dc_training.yaml')"
   ```

3. **数据加载错误**
   - 检查数据路径是否正确
   - 确认数据集格式符合预期

### 调试技巧

1. **使用调试模式**
   ```bash
   ./run_dc_training.sh --debug
   ```

2. **检查模型参数**
   - 训练开始时会打印参数统计信息
   - 检查DC参数是否正确初始化

3. **监控训练指标**
   - 查看Wandb面板中的损失曲线
   - 检查对比误差矩阵的分布

## 扩展和自定义

### 添加新的对比学习策略
在`agents/dc_ddpm_agent.py`中修改`contrastive_loss`方法。

### 修改DC tokens架构
在配置文件中调整`n_dc_tokens`、`n_dc_layers`等参数。

### 集成新的数据集
修改`config/dc_training.yaml`中的数据集配置。

### 性能优化

1. **使用混合精度训练**
   ```yaml
   dc_training:
     use_mixed_precision: true
   ```

2. **调整数据加载器**
   ```yaml
   data_loading:
     num_workers: 4
     pin_memory: true
     persistent_workers: true
   ```

3. **梯度累积**
   ```yaml
   dc_training:
     accumulation_steps: 4
   ```

## 与 SoftREPA 的对比

| 特性 | SoftREPA | MaIL 实现 |
|------|----------|-----------|
| DC tokens 形状 | `(n_dc_layers, n_dc_tokens, embed_dim)` | ✅ 相同 |
| 时间依赖性 | 支持 DC_T tokens | ✅ 相同 |
| 注入方式 | 在文本序列前拼接 | 🔄 在每层输入前拼接 |
| 序列处理 | 保留DC tokens到最后 | 🔄 每层后移除DC tokens |
| 初始化方式 | 正态分布 (std=0.02) | ✅ 相同 |

## 优势

1. **灵活性**：可以独立控制每层的DC tokens
2. **效率**：每层处理后移除DC tokens，减少计算开销
3. **兼容性**：与现有MaIL架构完全兼容
4. **可扩展性**：易于扩展到其他transformer架构

## 总结

成功在 MaIL 项目中集成了 DC tokens 机制，实现了：
- 轻量级的可学习参数扩展
- 层级化的特征增强
- 时间依赖的动态调整
- 完整的测试验证
- 完善的训练流程

该实现为 MaIL 项目提供了类似 SoftREPA 的对比学习能力，可用于改善模型的条件生成性能。

## 联系和支持

如果遇到问题或需要帮助，请检查：
1. 配置文件是否正确
2. 数据路径是否存在
3. CUDA环境是否正确设置
4. 依赖包是否安装完整 