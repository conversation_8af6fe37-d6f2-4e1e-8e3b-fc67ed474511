# DC训练工作流程需求文档

## 简介

本文档概述了MaIL项目中DC（判别/对比）tokens训练工作流程的需求。DC训练系统实现了SoftREPA风格的对比学习，以增强机器人操作任务的扩散策略模型。该系统通过轻量级DC tokens扩展标准扩散模型，在保持原始模型架构的同时启用对比学习。

## 需求

### 需求1：DC训练配置管理

**用户故事：** 作为研究人员，我希望通过YAML文件配置DC训练参数，这样我就可以轻松地尝试不同的超参数和训练设置。

#### 验收标准

1. 当用户指定DC训练配置时，系统应从`config/dc_training.yaml`加载参数
2. 当启动DC训练时，系统应验证所有必需的配置参数
3. 如果配置参数缺失，系统应提供清晰的错误消息和默认值
4. 当配置包含DC架构设置时，系统应独立支持编码器和解码器DC层
5. 当指定对比学习参数时，系统应相应地配置温度、缩放和损失权重

### 需求2：DC模型架构集成

**用户故事：** 作为开发人员，我希望DC tokens能够无缝集成到现有的扩散模型中，这样我就可以在不破坏现有功能的情况下增强模型能力。

#### 验收标准

1. 当DC tokens初始化时，系统应将它们注入到指定的编码器和解码器层中
2. 当模型处理序列时，DC tokens应在层输入时添加，处理后移除
3. 如果DC tokens被禁用，系统应与原始扩散模型功能完全相同
4. 当请求DC参数时，系统应只返回与DC相关的参数用于优化
5. 当启用基础模型冻结时，系统应冻结所有非DC参数，同时保持DC tokens可训练

### 需求3：对比学习实现

**用户故事：** 作为研究人员，我希望使用带有情节感知负采样的对比学习来训练模型，这样我就可以改善机器人任务中的动作-观察对齐。

#### 验收标准

1. 当计算对比误差时，系统应创建包含所有动作-观察对的扩展批次
2. 当处理情节时，系统应维护情节边界以进行适当的负采样
3. 当计算对比损失时，系统应应用带有时间窗口的情节感知掩码
4. 如果启用困难负采样，系统应根据情节接近度对负样本进行加权
5. 当启用分散损失时，系统应鼓励潜在空间中的特征多样性

### 需求4：训练循环管理

**用户故事：** 作为研究人员，我希望有一个具有全面监控和检查点功能的鲁棒训练循环，这样我就可以跟踪训练进度并从中断中恢复。

#### 验收标准

1. 当训练开始时，系统应初始化日志记录、wandb跟踪和检查点目录
2. 当处理训练批次时，系统应计算单独的损失组件（对比、扩散、分散）
3. 当达到评估频率时，系统应在验证数据上评估模型
4. 如果验证损失改善，系统应保存最佳模型检查点
5. 当训练完成时，系统应保存最终检查点并提供评估命令

### 需求5：数据加载和批次处理

**用户故事：** 作为开发人员，我希望有高效的数据加载和适当的批次格式转换，这样训练就可以处理大型数据集而不会出现内存问题。

#### 验收标准

1. 当加载训练数据时，系统应使用配置的批次大小和工作进程数
2. 当转换批次格式时，系统应将原始数据转换为预期的模型输入格式
3. 当将数据移动到设备时，系统应高效处理GPU内存
4. 如果情节索引可用，系统应为情节感知对比学习保留它们
5. 当批次处理失败时，系统应记录错误并继续处理下一个批次

### 需求6：模型评估和验证

**用户故事：** 作为研究人员，我希望在训练期间和完成后进行自动化模型评估，这样我就可以在无需手动干预的情况下评估模型性能。

#### 验收标准

1. 当触发验证时，系统应在验证数据集上评估模型
2. 当计算验证损失时，系统应使用与训练相同的损失函数
3. 如果评估失败，系统应记录错误并返回默认损失值
4. 当训练完成时，系统应自动评估最佳和最新模型
5. 当评估结果可用时，系统应记录成功率并将结果保存到JSON文件

### 需求7：检查点管理和模型持久化

**用户故事：** 作为研究人员，我希望有可靠的模型检查点和自动清理功能，这样我就可以恢复训练并高效管理存储。

#### 验收标准

1. 当保存检查点时，系统应包含模型状态、优化器状态和训练元数据
2. 当达到最佳验证损失时，系统应保存专用的最佳模型检查点
3. 如果启用EMA，系统应在检查点中包含EMA状态
4. 当超过检查点限制时，系统应自动删除最旧的检查点
5. 当加载预训练权重时，系统应支持文件路径和带有默认文件名的目录路径

### 需求8：日志记录和监控集成

**用户故事：** 作为研究人员，我希望有全面的日志记录和wandb集成，这样我就可以有效地监控训练进度和分析结果。

#### 验收标准

1. 当训练开始时，系统应为主要事件和详细训练日志初始化单独的记录器
2. 当记录训练指标时，系统应记录损失组件、学习率和样本统计信息
3. 如果启用wandb，系统应使用适当的标签和项目组织将指标记录到wandb
4. 当发生错误时，系统应记录详细的错误信息和堆栈跟踪
5. 当训练完成时，系统应提供摘要统计信息和文件位置

### 需求9：命令行界面和脚本集成

**用户故事：** 作为用户，我希望有一个简单的DC训练命令行界面，这样我就可以轻松地使用不同的配置和参数开始训练。

#### 验收标准

1. 当使用训练脚本时，系统应支持命令行参数覆盖
2. 当指定调试模式时，系统应使用减少的参数进行快速测试
3. 如果提供预训练模型路径，系统应在开始DC训练前加载权重
4. 当请求帮助时，系统应显示带有示例的使用信息
5. 当训练完成时，系统应提供下一步和评估命令

### 需求10：错误处理和恢复

**用户故事：** 作为开发人员，我希望在整个训练管道中有鲁棒的错误处理，这样即使个别批次失败或出现临时问题，训练也能继续进行。

#### 验收标准

1. 当批次处理失败时，系统应记录错误并继续处理下一个批次
2. 当验证失败时，系统应记录错误并使用默认验证损失
3. 如果GPU内存耗尽，系统应提供清晰的错误消息和建议
4. 当配置无效时，系统应提供指出问题的具体错误消息
5. 当训练中断时，系统应保存当前状态并提供恢复说明